package net.summerfarm.payment.routing.service.impl;

import net.summerfarm.payment.routing.common.enums.PaymentBusinessLineEnums;
import net.summerfarm.payment.routing.common.enums.PaymentChannelProviderEnums;
import net.summerfarm.payment.routing.dao.CompanyAccountMapper;
import net.summerfarm.payment.routing.dao.PaymentChannelDAO;
import net.summerfarm.payment.routing.dao.PaymentRuleDAO;
import net.summerfarm.payment.routing.dao.PaymentRuleRoutingDAO;
import net.summerfarm.payment.routing.model.domain.PaymentChannel;
import net.summerfarm.payment.routing.model.domain.PaymentRule;
import net.summerfarm.payment.routing.model.domain.PaymentRuleRouting;
import net.summerfarm.payment.routing.model.dto.PaymentChannelSaveDTO;
import net.xianmu.common.exception.ParamsException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * @description: PaymentChannelServiceImpl单元测试
 * @author: Augment Agent
 * @date: 2025-08-15
 **/
@ExtendWith(MockitoExtension.class)
class PaymentChannelServiceImplTest {

    @Mock
    private PaymentChannelDAO paymentChannelDAO;

    @Mock
    private PaymentRuleDAO paymentRuleDAO;

    @Mock
    private PaymentRuleRoutingDAO paymentRuleRoutingDAO;

    @Mock
    private CompanyAccountMapper companyAccountMapper;

    @InjectMocks
    private PaymentChannelServiceImpl paymentChannelService;

    private PaymentChannelSaveDTO saasChannelSaveDTO;
    private PaymentChannelSaveDTO summerfarmChannelSaveDTO;

    @BeforeEach
    void setUp() {
        // 准备SaaS业务线测试数据
        saasChannelSaveDTO = new PaymentChannelSaveDTO();
        saasChannelSaveDTO.setTenantId(1001L);
        saasChannelSaveDTO.setBusinessLine(PaymentBusinessLineEnums.SAAS.getCode());
        saasChannelSaveDTO.setChannelName(PaymentChannelProviderEnums.DIN_PAY.getChannelName());
        saasChannelSaveDTO.setCompanyEntity("测试SaaS企业");
        saasChannelSaveDTO.setMerchantNo("SAAS123456");
        saasChannelSaveDTO.setSecret("test_secret");
        saasChannelSaveDTO.setPrivateKey("test_private_key");
        saasChannelSaveDTO.setPublicKey("test_public_key");
        saasChannelSaveDTO.setOperatorAdminId(1L);
        // SaaS业务线专用字段
        saasChannelSaveDTO.setSceneName("小程序微信支付");
        saasChannelSaveDTO.setPlatform("miniapp");
        saasChannelSaveDTO.setPaymentMethod("wechat");

        // 准备鲜沐商城业务线测试数据
        summerfarmChannelSaveDTO = new PaymentChannelSaveDTO();
        summerfarmChannelSaveDTO.setTenantId(1L);
        summerfarmChannelSaveDTO.setBusinessLine(PaymentBusinessLineEnums.SUMMERFARM.getCode());
        summerfarmChannelSaveDTO.setChannelName(PaymentChannelProviderEnums.DIN_PAY.getChannelName());
        summerfarmChannelSaveDTO.setCompanyEntity("测试鲜沐企业");
        summerfarmChannelSaveDTO.setMerchantNo("SF123456");
        summerfarmChannelSaveDTO.setSecret("test_secret");
        summerfarmChannelSaveDTO.setPrivateKey("test_private_key");
        summerfarmChannelSaveDTO.setPublicKey("test_public_key");
        summerfarmChannelSaveDTO.setOperatorAdminId(1L);
    }

    @Test
    void testSaveChannel_SaasBusiness_Success() {
        // Given
        when(paymentChannelDAO.selectByUnique(any(), any(), any(), any(), any())).thenReturn(null);
        when(paymentChannelDAO.insert(any(PaymentChannel.class))).thenAnswer(invocation -> {
            PaymentChannel channel = invocation.getArgument(0);
            channel.setId(100L);
            return 1;
        });
        when(paymentRuleDAO.insert(any(PaymentRule.class))).thenAnswer(invocation -> {
            PaymentRule rule = invocation.getArgument(0);
            rule.setId(200L);
            return 1;
        });
        when(paymentRuleRoutingDAO.insert(any(PaymentRuleRouting.class))).thenReturn(1);

        // When
        Long channelId = paymentChannelService.saveChannel(saasChannelSaveDTO);

        // Then
        assertNotNull(channelId);
        assertEquals(100L, channelId);

        // 验证不会创建CompanyAccount
        verify(companyAccountMapper, never()).insert(any());

        // 验证创建PaymentChannel
        ArgumentCaptor<PaymentChannel> channelCaptor = ArgumentCaptor.forClass(PaymentChannel.class);
        verify(paymentChannelDAO).insert(channelCaptor.capture());
        PaymentChannel savedChannel = channelCaptor.getValue();
        assertEquals(saasChannelSaveDTO.getTenantId(), savedChannel.getTenantId());
        assertEquals(saasChannelSaveDTO.getBusinessLine(), savedChannel.getBusinessLine());
        assertNull(savedChannel.getCompanyAccountId()); // SaaS业务线不设置CompanyAccountId

        // 验证创建PaymentRule
        ArgumentCaptor<PaymentRule> ruleCaptor = ArgumentCaptor.forClass(PaymentRule.class);
        verify(paymentRuleDAO).insert(ruleCaptor.capture());
        PaymentRule savedRule = ruleCaptor.getValue();
        assertEquals(saasChannelSaveDTO.getTenantId(), savedRule.getTenantId());
        assertEquals(saasChannelSaveDTO.getBusinessLine(), savedRule.getBusinessLine());
        assertEquals(saasChannelSaveDTO.getSceneName(), savedRule.getSceneName());
        assertEquals(saasChannelSaveDTO.getPlatform(), savedRule.getPlatform());
        assertEquals(saasChannelSaveDTO.getPaymentMethod(), savedRule.getPaymentMethod());
        assertEquals(100L, savedRule.getChannelId());

        // 验证创建PaymentRuleRouting
        ArgumentCaptor<PaymentRuleRouting> routingCaptor = ArgumentCaptor.forClass(PaymentRuleRouting.class);
        verify(paymentRuleRoutingDAO).insert(routingCaptor.capture());
        PaymentRuleRouting savedRouting = routingCaptor.getValue();
        assertEquals(saasChannelSaveDTO.getTenantId(), savedRouting.getTenantId());
        assertEquals(saasChannelSaveDTO.getBusinessLine(), savedRouting.getBusinessLine());
        assertEquals("tenant_id", savedRouting.getRouteKey());
        assertEquals(saasChannelSaveDTO.getTenantId().toString(), savedRouting.getRouteValue());
        assertEquals(200L, savedRouting.getRuleId());
    }

    @Test
    void testSaveChannel_SummerfarmBusiness_Success() {
        // Given
        when(paymentChannelDAO.selectByUnique(any(), any(), any(), any(), any())).thenReturn(null);
        when(companyAccountMapper.insert(any())).thenAnswer(invocation -> {
            invocation.getArgument(0, net.summerfarm.payment.routing.model.domain.CompanyAccount.class).setId(50);
            return 1;
        });
        when(paymentChannelDAO.insert(any(PaymentChannel.class))).thenAnswer(invocation -> {
            PaymentChannel channel = invocation.getArgument(0);
            channel.setId(100L);
            return 1;
        });

        // When
        Long channelId = paymentChannelService.saveChannel(summerfarmChannelSaveDTO);

        // Then
        assertNotNull(channelId);
        assertEquals(100L, channelId);

        // 验证创建了CompanyAccount
        verify(companyAccountMapper).insert(any());

        // 验证创建PaymentChannel时设置了CompanyAccountId
        ArgumentCaptor<PaymentChannel> channelCaptor = ArgumentCaptor.forClass(PaymentChannel.class);
        verify(paymentChannelDAO).insert(channelCaptor.capture());
        PaymentChannel savedChannel = channelCaptor.getValue();
        assertEquals(Integer.valueOf(50), savedChannel.getCompanyAccountId());

        // 验证不会创建SaaS相关的PaymentRule和PaymentRuleRouting
        verify(paymentRuleDAO, never()).insert(any());
        verify(paymentRuleRoutingDAO, never()).insert(any());
    }

    @Test
    void testSaveChannel_SaasBusiness_MissingSceneName_ThrowsException() {
        // Given
        saasChannelSaveDTO.setSceneName(null);

        // When & Then
        ParamsException exception = assertThrows(ParamsException.class,
            () -> paymentChannelService.saveChannel(saasChannelSaveDTO));
        assertEquals("场景名称不能为空", exception.getMessage());
    }

    @Test
    void testSaveChannel_SaasBusiness_MissingPlatform_ThrowsException() {
        // Given
        saasChannelSaveDTO.setPlatform("");

        // When & Then
        ParamsException exception = assertThrows(ParamsException.class,
            () -> paymentChannelService.saveChannel(saasChannelSaveDTO));
        assertEquals("应用平台不能为空", exception.getMessage());
    }

    @Test
    void testSaveChannel_SaasBusiness_MissingPaymentMethod_ThrowsException() {
        // Given
        saasChannelSaveDTO.setPaymentMethod(null);

        // When & Then
        ParamsException exception = assertThrows(ParamsException.class,
            () -> paymentChannelService.saveChannel(saasChannelSaveDTO));
        assertEquals("支付方式不能为空", exception.getMessage());
    }
}
