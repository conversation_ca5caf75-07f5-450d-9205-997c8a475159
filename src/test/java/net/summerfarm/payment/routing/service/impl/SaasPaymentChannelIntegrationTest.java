package net.summerfarm.payment.routing.service.impl;

import net.summerfarm.payment.routing.common.enums.PaymentBusinessLineEnums;
import net.summerfarm.payment.routing.common.enums.PaymentChannelProviderEnums;
import net.summerfarm.payment.routing.model.dto.PaymentChannelSaveDTO;
import org.junit.jupiter.api.Test;

/**
 * @description: SaaS业务线支付渠道集成测试示例
 * @author: Augment Agent
 * @date: 2025-08-15
 **/
public class SaasPaymentChannelIntegrationTest {

    /**
     * 演示如何为SaaS业务线创建支付渠道
     * 注意：这是一个示例测试，实际使用时需要配置Spring上下文
     */
    @Test
    void demonstrateSaasChannelCreation() {
        // 创建SaaS业务线的支付渠道保存参数
        PaymentChannelSaveDTO saasChannelDTO = new PaymentChannelSaveDTO();

        // 基础信息
        saasChannelDTO.setTenantId(1001L); // SaaS租户ID
        saasChannelDTO.setBusinessLine(PaymentBusinessLineEnums.SAAS.getCode()); // "saas"
        saasChannelDTO.setChannelName(PaymentChannelProviderEnums.DIN_PAY.getChannelName()); // "智付间联"
        saasChannelDTO.setCompanyEntity("SaaS测试企业"); // 企业主体（SaaS业务线仍需要，但不会创建CompanyAccount）
        saasChannelDTO.setMerchantNo("SAAS_MERCHANT_001");
        saasChannelDTO.setOperatorAdminId(1L);

        // 智付渠道必需参数
        saasChannelDTO.setSecret("saas_merchant_secret");
        saasChannelDTO.setPrivateKey("saas_merchant_private_key");
        saasChannelDTO.setPublicKey("dinpay_platform_public_key");

        // SaaS业务线专用参数 - 用于创建PaymentRule
        saasChannelDTO.setSceneName("小程序微信支付"); // 场景名称
        saasChannelDTO.setPlatform("miniapp"); // 应用平台：miniapp、h5等
        saasChannelDTO.setPaymentMethod("wechat"); // 支付方式：wechat、alipay等

        // 调用服务创建渠道
        // PaymentChannelService service = ...; // 需要Spring上下文注入
        // Long channelId = service.saveChannel(saasChannelDTO);

        // 预期结果：
        // 1. 创建PaymentChannel记录，但不设置companyAccountId
        // 2. 创建PaymentRule记录，包含场景名称、平台、支付方式等信息
        // 3. 创建PaymentRuleRouting记录，routeKey="tenant_id", routeValue="1001"

        System.out.println("SaaS业务线支付渠道创建示例:");
        System.out.println("租户ID: " + saasChannelDTO.getTenantId());
        System.out.println("业务线: " + saasChannelDTO.getBusinessLine());
        System.out.println("渠道名称: " + saasChannelDTO.getChannelName());
        System.out.println("场景名称: " + saasChannelDTO.getSceneName());
        System.out.println("应用平台: " + saasChannelDTO.getPlatform());
        System.out.println("支付方式: " + saasChannelDTO.getPaymentMethod());
    }

    /**
     * 演示不同场景的SaaS支付渠道配置
     */
    @Test
    void demonstrateDifferentSaasScenarios() {
        // 场景1：小程序微信支付
        PaymentChannelSaveDTO miniAppWechat = createBaseSaasChannel(2001L);
        miniAppWechat.setSceneName("小程序微信支付");
        miniAppWechat.setPlatform("miniapp");
        miniAppWechat.setPaymentMethod("wechat");

        // 场景2：H5支付宝支付
        PaymentChannelSaveDTO h5Alipay = createBaseSaasChannel(2002L);
        h5Alipay.setSceneName("H5支付宝支付");
        h5Alipay.setPlatform("h5");
        h5Alipay.setPaymentMethod("alipay");

        // 场景3：公众号微信支付
        PaymentChannelSaveDTO jsapiWechat = createBaseSaasChannel(2003L);
        jsapiWechat.setSceneName("公众号微信支付");
        jsapiWechat.setPlatform("jsapi");
        jsapiWechat.setPaymentMethod("wechat");

        System.out.println("=== SaaS业务线多场景支付渠道配置示例 ===");
        printChannelInfo("小程序微信支付", miniAppWechat);
        printChannelInfo("H5支付宝支付", h5Alipay);
        printChannelInfo("公众号微信支付", jsapiWechat);
    }

    private PaymentChannelSaveDTO createBaseSaasChannel(Long tenantId) {
        PaymentChannelSaveDTO dto = new PaymentChannelSaveDTO();
        dto.setTenantId(tenantId);
        dto.setBusinessLine(PaymentBusinessLineEnums.SAAS.getCode());
        dto.setChannelName(PaymentChannelProviderEnums.DIN_PAY.getChannelName());
        dto.setCompanyEntity("SaaS租户" + tenantId);
        dto.setMerchantNo("MERCHANT_" + tenantId);
        dto.setSecret("secret_" + tenantId);
        dto.setPrivateKey("private_key_" + tenantId);
        dto.setPublicKey("public_key_" + tenantId);
        dto.setOperatorAdminId(1L);
        return dto;
    }

    private void printChannelInfo(String title, PaymentChannelSaveDTO dto) {
        System.out.println("\n" + title + ":");
        System.out.println("  租户ID: " + dto.getTenantId());
        System.out.println("  场景名称: " + dto.getSceneName());
        System.out.println("  应用平台: " + dto.getPlatform());
        System.out.println("  支付方式: " + dto.getPaymentMethod());
        System.out.println("  预期路由: tenant_id=" + dto.getTenantId());
    }
}
